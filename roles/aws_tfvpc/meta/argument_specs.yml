---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        argument_specs.yml                                                #
# Version:                                                                        #
#               2024-01-24 WRC. Initial                                           #
# Create Date:  2024-01-24                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is the argument specification for the role. With this file   #
#               present, a new task is inserted at the beginning of role          #
#               execution that will validate the parameters supplied for the role #
#               against the specification. If the parameters fail validation, the #
#               role will fail execution.                                         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
# Reference:                                                                      #
# https://docs.ansible.com/ansible/latest/collections/ansible/builtin/validate_argument_spec_module.html
argument_specs:
  aws_tfvpc_main:
    author: Casagrande, <PERSON> (US) <<EMAIL>>
    short_description: Galaxy Amazon Web Services Terraform Root role entrypoint
    description:  Galaxy Amazon Web Services main entry point for the Terraform root
                  role is used to combines the discrete tasks in this role and build
                  out the Terraform root.

    # ----------------------------------------------- #
    # Options are “parameters” or “variables”.        #
    # ----------------------------------------------- #
    options: {}
