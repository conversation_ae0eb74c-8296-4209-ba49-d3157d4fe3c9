# AWS Terraform VPC (lmco.aws.aws_tfvpc)

This role is used to manage the Terraform Virtual Private Cloud (AWS VPC) resources use to setup a new or existing AWS account. An AWS Virtual Private Cloud (AWS VPC) is the typical boundary defined within and AWS account and often requires VPC level resources like storage and subnets. This role uses integrated assets from the Enterprise OpenShift code base and uses Terraform as the orchestration engine.

## Authors

This role is maintained by the [Lockheed Martin Galaxy Team](https://galaxy.pages.gitlab.global.lmco.com/documents/about/team/). Please see our [Feedback Page](https://galaxy.pages.gitlab.global.lmco.com/documents/feedback/) if you would like to pass along ideas or suggestions.

## Dependencies

Dependencies are defined at the collection level in the [Requirements](../../requirements.yml) configuration.

## Requirements

Ansible requirements are defined in the [Role Metadata](meta/main.yml) for this role.

## Parameters

Role variables are defined in the [Argument Specification](meta/argument_specs.yml) for this role and are listed by Entry Point.

## Example Playbook

This role contains a specific Entry Point that coordinates the execution of tasks. The will support invocation as part of a Galaxy bundle but may also be invoked directly using the following playbook definition.

```yaml
 tasks:
    - ansible.builtin.include_role:
        name: aws_tfstate
        tasks_from: aws_tfstate_main
```

## License

License terms are defined at the collection level in the [LICENSE](../../LICENSE) file.
