# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        providers.tf.j2                                                   #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-25 WRC. Update for Galaxy Ansible                         #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Provider configuration for VPC-only testing                      #
#                                                                                 #
# ------------------------------------------------------------------------------- #

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }
}

#-----------------------------------------------#
# Primary AWS Provider Configuration            #
#-----------------------------------------------#
provider "aws" {
  alias                     = "{{ aws_region.name }}"
  region                    = "{{ aws_region.name }}"
  profile                   = "{{ aws_profile }}"
  shared_credentials_files  = [ "{{ aws_tfvpc_tempdir.path }}/credentials" ]
  allowed_account_ids       = [ "{{ aws_account.id }}" ]
  max_retries               = 50
  insecure                  = false

  default_tags {
    tags = {
      Environment = "{{ aws_environment.id }}"
      Account     = "{{ aws_account.id }}"
      Region      = "{{ aws_region.name }}"
      ManagedBy   = "Terraform"
      Purpose     = "VPC-Testing"
    }
  }
}
