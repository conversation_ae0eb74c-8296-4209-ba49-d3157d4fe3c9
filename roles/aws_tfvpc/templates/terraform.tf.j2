# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        terraform.tf.j2                                                   #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-25 WRC. Update for Galaxy Ansible                         #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Terraform backend configuration for VPC-only testing             #
#                                                                                 #
# ------------------------------------------------------------------------------- #

terraform {
  backend "s3" {
    # Backend configuration will be provided via init command
    # bucket         = "{{ aws_s3_bucket.name }}"
    # key            = "{{ aws_prefix }}/vpc/terraform.tfstate"
    # region         = "{{ aws_region.name }}"
    # dynamodb_table = "{{ aws_prefix }}-tfstate"
    # encrypt        = true
  }
}
