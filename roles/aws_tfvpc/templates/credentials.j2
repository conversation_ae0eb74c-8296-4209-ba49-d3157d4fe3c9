# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        credentials.j2                                                    #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-25 WRC. Update for Galaxy Ansible                         #
#               2021-08-10 WRC. Initial                                           #
# Create Date:  2021-08-10                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               AWS credentials file for VPC-only testing                        #
#                                                                                 #
# ------------------------------------------------------------------------------- #

[{{ aws_profile }}]
aws_access_key_id = {{ aws_common_vars.aws_access_key_id }}
aws_secret_access_key = {{ aws_common_vars.aws_secret_access_key }}
{% if aws_common_vars.aws_session_token is defined %}
aws_session_token = {{ aws_common_vars.aws_session_token }}
{% endif %}
