---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_apply.yml                                               #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Apply the Terraform VPC configuration                            #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute the Terraform VPC module
  community.general.terraform:            # as of 2023-02-01 cloud.terraform.terraform currently lagging and buggy
    state: "{{ aws_common_state }}"       # The value `planned` for the parameter "state" is deprecated.
    # binary_path: "/usr/bin/"                    # The path of a terraform binary to use.
    project_path: "{{ aws_tfvpc_tempdir.path }}"
    check_destroy: true                           # Apply only when no resources are destroyed
    # plan_file: "./test.plan"                    # Not used with backend config in current version
    lock: true                                    # Enable statefile locking
    lock_timeout: 30                              # How long to maintain the lock on the statefile
    provider_upgrade: true                        # Allows Terraform init to upgrade providers to versions specified in the project's version constraints.

    # --------------------------------- #
    # Initialization configuration      #
    # --------------------------------- #
    force_init: true                             # Generally, this should be turned off unless you intend to provision an entirely new Terraform deployment
    init_reconfigure: true
    overwrite_init: false

    # --------------------------------- #
    # Backend configuration             #
    # --------------------------------- #
    backend_config:
      bucket:         "{{ aws_s3_bucket.name }}"
      key:            "{{ aws_prefix }}/vpc/terraform.tfstate"
      region:         "{{ aws_region.name }}"
      # dynamodb_table: "{{ aws_prefix }}-tfstate"  # Disabled for VPC-only testing
      encrypt:        true

    # ------------------------------------------------------------- #
    # Group of key-values pairs to override template variables or   #
    # those in variables files.                                     #
    # ------------------------------------------------------------- #
    # Note that had to use JSON as the intermediate format for      #
    # Ansible -> Terraform as the Ansible dict passed in as string  #
    # that Terraform did not treat as an object.                    #
    # ------------------------------------------------------------- #
    # https://stackoverflow.com/questions/69794178/convert-json-to-terraform-mapobjects     #
    complex_vars: true
    variables:
      common_data: "{{ aws_common_data | to_json }}"

    workspace: default

  # ------------------- #
  # Suported Options    #
  # ------------------- #
  check_mode: true   # Will only init if in this mode
  register: aws_tfvpc_terraform_out

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_terraform_out: "{{ aws_tfvpc_terraform_out }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
