---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_cleanup.yml                                             #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Cleanup temporary files after Terraform VPC execution           #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Remove temporary directory
  ansible.builtin.file:
    path: "{{ aws_tfvpc_tempdir.path }}"
    state: absent
  when: aws_tfvpc_tempdir is defined

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      cleanup_completed: true
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
