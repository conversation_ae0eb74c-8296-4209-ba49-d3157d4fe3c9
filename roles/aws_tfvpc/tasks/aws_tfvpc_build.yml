---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_build.yml                                               #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               Build the Terraform VPC configuration from templates             #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Create temporary directory for VPC Terraform configuration
  ansible.builtin.tempfile:
    state: directory
    suffix: .tfvpc
  register: aws_tfvpc_tempdir

# ------------------------------------------------------------------------------- #
- name: Generate main.tf from template
  ansible.builtin.template:
    src: "{{ role_path }}/templates/main.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/main.tf"
    mode: '0644'

- name: Generate providers.tf from template
  ansible.builtin.template:
    src: "{{ role_path }}/templates/providers.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/providers.tf"
    mode: '0644'

- name: Generate terraform.tf from template
  ansible.builtin.template:
    src: "{{ role_path }}/templates/terraform.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/terraform.tf"
    mode: '0644'

- name: Generate credentials file from template
  ansible.builtin.template:
    src: "{{ role_path }}/templates/credentials.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/credentials"
    mode: '0600'

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
