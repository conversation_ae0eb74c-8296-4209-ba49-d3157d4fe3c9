---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        tasks/main.yml                                                    #
# Version:                                                                        #
#               2024-10-04 WRC. VPC-only testing version                          #
#               2024-01-30 WRC. Initial                                           #
# Create Date:  2024-01-30                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               Ansible main entry point for the tfvpc role.                     #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
      vpc_init: "{{ vpc_init | default(true) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
- name: Execute aws_tfvpc_build task
  ansible.builtin.include_tasks: aws_tfvpc_build.yml

- name: Execute aws_tfvpc_apply task
  ansible.builtin.include_tasks: aws_tfvpc_apply.yml

- name: Execute aws_tfvpc_cleanup task
  ansible.builtin.include_tasks: aws_tfvpc_cleanup.yml

# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
      aws_tfvpc_tempdir: "{{ aws_tfvpc_tempdir }}"
      vpc_outputs: "{{ aws_tfvpc_terraform_out.outputs | default({}) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
