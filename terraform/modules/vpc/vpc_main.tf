#############################################################################################
#                                                                                           #
# Title:        vpc_main.tf                                                                 #
# Version:                                                                                  #
#               2021-11-09 WRC. Add provider def to eliminate warnings in latest version    #
#               2020-12-07 WRC. Add outputs                                                 #
#               2020-11-10 WRC. Converge variables and outputs in this file.                #
#               2020-06-09 WRC. Initial                                                     #
# Create Date:  2020-06-09                                                                  #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                          #
# Description:                                                                              #
#               Established the terraform state this account and environment                #
#                                                                                           #
#############################################################################################
# terraform {
#   required_providers { aws = {} }
# }

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
variable "account"      { type = string }
variable "region"       { type = string }
variable "environment"  { type = string }
variable "vpc_init"     {
                          type = bool
                          default = false
                        }
variable "common_data" {}

#-------------------------------------------------------------------------------------------#
# Data Section                                                                              #
#-------------------------------------------------------------------------------------------#
data "aws_availability_zones" "available" {
  state = "available"
}

#-------------------------------------------------------------------------------------------#
# Locals Section                                                                            #
#-------------------------------------------------------------------------------------------#
locals {
  vpc_cidr = "10.0.0.0/16"
  azs      = slice(data.aws_availability_zones.available.names, 0, 2)

  public_subnets  = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnets = ["10.0.101.0/24", "10.0.102.0/24"]

  common_tags = {
    Environment = var.environment
    Account     = var.account
    Region      = var.region
    ManagedBy   = "Terraform"
    Purpose     = "VPC-Testing"
  }
}

#-------------------------------------------------------------------------------------------#
# VPC Resources                                                                             #
#-------------------------------------------------------------------------------------------#
resource "aws_vpc" "main" {
  count = var.vpc_init ? 1 : 0

  cidr_block           = local.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-vpc"
  })
}

resource "aws_internet_gateway" "main" {
  count = var.vpc_init ? 1 : 0

  vpc_id = aws_vpc.main[0].id

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-igw"
  })
}

resource "aws_subnet" "public" {
  count = var.vpc_init ? length(local.public_subnets) : 0

  vpc_id                  = aws_vpc.main[0].id
  cidr_block              = local.public_subnets[count.index]
  availability_zone       = local.azs[count.index]
  map_public_ip_on_launch = true

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-public-subnet-${count.index + 1}"
    Type = "Public"
  })
}

resource "aws_subnet" "private" {
  count = var.vpc_init ? length(local.private_subnets) : 0

  vpc_id            = aws_vpc.main[0].id
  cidr_block        = local.private_subnets[count.index]
  availability_zone = local.azs[count.index]

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-private-subnet-${count.index + 1}"
    Type = "Private"
  })
}

resource "aws_route_table" "public" {
  count = var.vpc_init ? 1 : 0

  vpc_id = aws_vpc.main[0].id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main[0].id
  }

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-public-rt"
  })
}

resource "aws_route_table" "private" {
  count = var.vpc_init ? length(local.private_subnets) : 0

  vpc_id = aws_vpc.main[0].id

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-private-rt-${count.index + 1}"
  })
}

resource "aws_route_table_association" "public" {
  count = var.vpc_init ? length(aws_subnet.public) : 0

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public[0].id
}

resource "aws_route_table_association" "private" {
  count = var.vpc_init ? length(aws_subnet.private) : 0

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}

resource "aws_security_group" "default" {
  count = var.vpc_init ? 1 : 0

  name_prefix = "${var.common_data.prefix}-default-"
  vpc_id      = aws_vpc.main[0].id

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [local.vpc_cidr]
  }

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${var.common_data.prefix}-default-sg"
  })
}

#-------------------------------------------------------------------------------------------#
# Outputs Section                                                                           #
#-------------------------------------------------------------------------------------------#
output "vpc_data" {
  description = "The set of output data to make available to modules."
  sensitive   = false
  value = var.vpc_init ? {
    vpc_id              = aws_vpc.main[0].id
    vpc_cidr_block      = aws_vpc.main[0].cidr_block
    internet_gateway_id = aws_internet_gateway.main[0].id
    public_subnet_ids   = aws_subnet.public[*].id
    private_subnet_ids  = aws_subnet.private[*].id
    public_route_table_id = aws_route_table.public[0].id
    private_route_table_ids = aws_route_table.private[*].id
    default_security_group_id = aws_security_group.default[0].id
    availability_zones  = local.azs
  } : {}
}